import { styled, t } from '@superset-ui/core';

interface FolderData {
  id: string;
  title: string;
  subtitle: string;
  dashboardCount: number;
}

interface BusinessDomain {
  id: string;
  name: string;
  color: string;
}

interface FolderHeaderProps {
  currentFolder?: FolderData | null;
  currentBusiness?: BusinessDomain;
}

const HeaderContainer = styled.div`
  padding: ${({ theme }) => theme.gridUnit * 4}px
    ${({ theme }) => theme.gridUnit * 6}px;
  background-color: ${({ theme }) => theme.colors.grayscale.light5};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const HeaderContent = styled.div`
  display: flex;
  flex-direction: column;
`;

const HeaderTitleContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.gridUnit}px;
`;

const HeaderTitle = styled.p`
  margin: 0;
  font-size: ${({ theme }) => theme.typography.sizes.xl}px;
  color: ${({ theme }) => theme.colors.grayscale.dark1};
`;

const HeaderSubtitle = styled.p`
  font-size: ${({ theme }) => theme.typography.sizes.m}px;
  color: ${({ theme }) => theme.colors.text.label};
`;

const FolderHeader = ({
  currentFolder,
  currentBusiness,
}: FolderHeaderProps) => (
  <HeaderContainer>
    <HeaderContent>
      <HeaderTitleContainer>
        <HeaderTitle>{currentFolder?.title || 'Select a folder'}</HeaderTitle>
      </HeaderTitleContainer>
      <HeaderSubtitle>
        {currentFolder
          ? `${currentFolder.subtitle} • ${currentFolder.dashboardCount} ${t(
              'dashboards',
            )}`
          : t(
              'Choose a folder from %s to view dashboards',
              currentBusiness?.name,
            )}
      </HeaderSubtitle>
    </HeaderContent>
  </HeaderContainer>
);

export default FolderHeader;
