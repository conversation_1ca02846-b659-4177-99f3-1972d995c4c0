import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, Switch, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import Icons from 'src/components/Icons';
import { styled, t } from '@superset-ui/core';
import FaveStar from 'src/components/FaveStar';
import { TagsList } from 'src/components/Tags';
import Label from 'src/components/Label';
import CertifiedBadge from 'src/components/CertifiedBadge';
import { bootstrapData } from 'src/preamble';
import FacePile from 'src/components/FacePile';
import { ModifiedInfo } from 'src/components/AuditInfo';
import DashboardActions from './DashboardActions';
import { BusinessDomain, DashboardItem } from '../types';

const locale = bootstrapData?.common?.locale || 'en';

interface DashboardsSectionProps {
  dashboards: DashboardItem[];
  currentBusiness?: BusinessDomain;
  saveFavoriteStatus: (id: number, isStarred: boolean) => void;
  onDeleteDashboard?: (dashboard: DashboardItem) => void;
  onExportDashboard?: (dashboards: DashboardItem[]) => void;
  onEditDashboard?: (dashboard: DashboardItem) => void;
}

const SectionContainer = styled.div``;

const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.gridUnit * 4}px;
  padding-bottom: ${({ theme }) => theme.gridUnit * 2}px;
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
`;

const SectionIcon = styled(Icons.DashboardOutlined)<{ businessColor?: string }>`
  font-size: ${({ theme }) => theme.typography.sizes.l}px;
  color: ${({ businessColor, theme }) =>
    businessColor || theme.colors.primary.base};
  margin-right: ${({ theme }) => theme.gridUnit * 2}px;
`;

const SectionTitle = styled.p`
  margin: 0;
  font-size: ${({ theme }) => theme.typography.sizes.l}px;
  color: ${({ theme }) => theme.colors.grayscale.dark1};
  font-weight: ${({ theme }) => theme.typography.weights.bold};
`;

const ViewModeControls = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit * 2}px;
`;

const ViewModeIcon = styled.div<{ isActive?: boolean; businessColor?: string }>`
  display: flex;
  align-items: center;
  color: ${({ isActive, businessColor, theme }) =>
    isActive ? businessColor : theme.colors.grayscale.base};
  font-size: ${({ theme }) => theme.typography.sizes.l}px;
`;

const CardList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${({ theme }) => theme.gridUnit * 4}px;
`;

const DashboardCard = styled(Card)`
  && {
    border-radius: ${({ theme }) => theme.borderRadius}px;
    border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
    height: 100%;

    .ant-card-body {
      padding: ${({ theme }) => theme.gridUnit * 3}px;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }
`;

const CardHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.gridUnit * 1.5}px;
`;

const CardTitleContainer = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: ${({ theme }) => theme.gridUnit * 2}px;
  min-width: 0;
`;

const CardTitle = styled(Link)`
  font-size: ${({ theme }) => theme.typography.sizes.s + 1}px;
  font-weight: ${({ theme }) => theme.typography.weights.bold};
  color: ${({ theme }) => theme.colors.grayscale.dark1};
  line-height: 1.3;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
`;

const CardRightSection = styled.div`
  display: flex;
  align-items: flex-start;
  flex-shrink: 0;
`;

const CardFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  font-size: ${({ theme }) => theme.typography.sizes.xs + 1}px;
  color: ${({ theme }) => theme.colors.text.label};
  margin-top: ${({ theme }) => theme.gridUnit * 3}px;
  padding-top: ${({ theme }) => theme.gridUnit * 2}px;
  border-top: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
`;

const DashboardsSection = ({
  dashboards,
  currentBusiness,
  saveFavoriteStatus,
  onDeleteDashboard,
  onExportDashboard,
  onEditDashboard,
}: DashboardsSectionProps) => {
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');
  const hasActions = onDeleteDashboard || onExportDashboard || onEditDashboard;

  if (dashboards.length === 0) {
    return null;
  }

  const renderTableView = () => {
    const columns: ColumnsType<DashboardItem> = [
      {
        key: 'favorite',
        render: (record: DashboardItem) => (
          <FaveStar
            itemId={record.id}
            saveFaveStar={saveFavoriteStatus}
            isStarred={record.isFavorite}
          />
        ),
      },
      { key: 'id', dataIndex: 'id', title: 'ID' },
      {
        title: t('Title (Eng)'),
        dataIndex: 'name_en',
        key: 'name_en',
        render: (text: string, record: DashboardItem) => {
          const { certifiedBy, id, name_en } = record;
          return (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Link to={`/superset/dashboard/${id}`}>
                {certifiedBy && (
                  <>
                    <CertifiedBadge certifiedBy={certifiedBy} />{' '}
                  </>
                )}
                {name_en}
              </Link>
            </div>
          );
        },
      },
      {
        title: t('Title (Rus)'),
        dataIndex: 'name_ru',
        key: 'name_ru',
        render: (text: string, record: DashboardItem) => {
          const { id, name_ru } = record;
          return (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Link to={`/superset/dashboard/${id}`}>{name_ru || '-'}</Link>
            </div>
          );
        },
      },
      {
        title: t('Status'),
        dataIndex: 'published',
        key: 'published',
        render: (published: boolean) => (
          <Label>{published ? t('Published') : t('Draft')}</Label>
        ),
      },
      {
        title: t('Tags'),
        dataIndex: 'tags',
        key: 'tags',
        render: (tags: { id: number; name: string; type: number }[]) => (
          <TagsList tags={tags} maxTags={3} />
        ),
      },
      {
        title: t('Owners'),
        dataIndex: 'owners',
        key: 'owners',
        render: owners => <FacePile users={owners} />,
      },
      {
        title: t('Last modified'),
        dataIndex: 'changedOn',
        key: 'changedOn',
        render: (changedOn: string) => <ModifiedInfo date={changedOn} />,
      },
    ];

    if (hasActions) {
      columns.push({
        title: t('Actions'),
        key: 'actions',
        render: (record: DashboardItem) => (
          <DashboardActions
            dashboard={record}
            onDeleteDashboard={onDeleteDashboard}
            onExportDashboard={onExportDashboard}
            onEditDashboard={onEditDashboard}
          />
        ),
      });
    }

    return (
      <Table
        dataSource={dashboards}
        columns={columns}
        rowKey="id"
        pagination={false}
        size="small"
        style={{ marginTop: '8px' }}
      />
    );
  };

  const renderCard = (dashboard: DashboardItem) => {
    const { certifiedBy, isFavorite, tags } = dashboard;
    const status = dashboard.published ? t('Published') : t('Draft');

    return (
      <DashboardCard size="small" key={dashboard.id}>
        <div style={{ flex: 1 }}>
          <CardHeader>
            <CardTitleContainer>
              {certifiedBy && <CertifiedBadge certifiedBy={certifiedBy} />}
              <CardTitle
                to={`/superset/dashboard/${dashboard.id}`}
                target="_blank"
                className="dashboard-title"
              >
                {locale === 'ru'
                  ? dashboard.name_ru || dashboard.name_en
                  : dashboard.name_en || dashboard.name_ru}
              </CardTitle>
            </CardTitleContainer>

            <CardRightSection>
              <Label style={{ margin: 0 }}>{status.toLowerCase()}</Label>
              <FaveStar
                itemId={dashboard.id}
                saveFaveStar={saveFavoriteStatus}
                isStarred={isFavorite}
              />
            </CardRightSection>
          </CardHeader>

          {tags.length > 0 && <TagsList tags={tags} maxTags={3} />}
        </div>

        {hasActions && (
          <CardFooter>
            <DashboardActions
              dashboard={dashboard}
              onDeleteDashboard={onDeleteDashboard}
              onExportDashboard={onExportDashboard}
              onEditDashboard={onEditDashboard}
            />
          </CardFooter>
        )}
      </DashboardCard>
    );
  };

  return (
    <SectionContainer>
      <SectionHeader>
        <HeaderLeft>
          <SectionIcon businessColor={currentBusiness?.color} />
          <SectionTitle>
            {t('Dashboards')} ({dashboards.length})
          </SectionTitle>
        </HeaderLeft>

        <ViewModeControls>
          <ViewModeIcon
            isActive={viewMode === 'cards'}
            businessColor={currentBusiness?.color}
          >
            <Icons.AppstoreOutlined iconSize="m" />
          </ViewModeIcon>
          <Switch
            checked={viewMode === 'table'}
            onChange={checked => setViewMode(checked ? 'table' : 'cards')}
            size="small"
          />
          <ViewModeIcon
            isActive={viewMode === 'table'}
            businessColor={currentBusiness?.color}
          >
            <Icons.UnorderedListOutlined iconSize="m" />
          </ViewModeIcon>
        </ViewModeControls>
      </SectionHeader>

      {viewMode === 'cards' ? (
        <CardList>{dashboards.map(renderCard)}</CardList>
      ) : (
        renderTableView()
      )}
    </SectionContainer>
  );
};

export default DashboardsSection;
